import plotly.graph_objects as go
import plotly.express as px
import json

# Enhanced data with security layer integration
data = {
    "layers": [
        {
            "name": "Frontend",
            "components": [
                {"name": "User Web App", "type": "frontend"},
                {"name": "Admin Dashboard", "type": "frontend"}
            ]
        },
        {
            "name": "Security/Gateway",
            "components": [
                {"name": "Load Balancer", "type": "infrastructure"},
                {"name": "JWT Auth", "type": "security"},
                {"name": "Rate Limiter", "type": "security"},
                {"name": "Data Encrypt", "type": "security"}
            ]
        },
        {
            "name": "Backend Svcs",
            "components": [
                {"name": "Auth Svc", "type": "microservice"},
                {"name": "Trans Svc", "type": "microservice"},
                {"name": "Billing Svc", "type": "microservice"},
                {"name": "Analytics Svc", "type": "microservice"},
                {"name": "User Mgmt Svc", "type": "microservice"}
            ]
        },
        {
            "name": "Database",
            "components": [
                {"name": "PostgreSQL", "type": "database"}
            ]
        },
        {
            "name": "External APIs",
            "components": [
                {"name": "Stripe/PayPal", "type": "external"},
                {"name": "M4T API", "type": "external"},
                {"name": "Email Svc", "type": "external"}
            ]
        }
    ]
}

# Color mapping for different component types
colors = {
    'frontend': '#1FB8CD',
    'infrastructure': '#DB4545', 
    'security': '#2E8B57',
    'microservice': '#5D878F',
    'database': '#D2BA4C',
    'external': '#B4413C'
}

# Create the figure
fig = go.Figure()

# Position components in layers with better spacing
y_positions = [4, 3, 2, 1, 0]  # Top to bottom
all_x = []
all_y = []
all_text = []
all_colors = []
all_hover = []

for i, layer in enumerate(data['layers']):
    layer_y = y_positions[i]
    
    # Calculate x positions for components in this layer with better spacing
    num_components = len(layer['components'])
    if num_components == 1:
        x_positions = [0]
    elif num_components == 2:
        x_positions = [-1, 1]
    elif num_components == 3:
        x_positions = [-1.5, 0, 1.5]
    elif num_components == 4:
        x_positions = [-2, -0.7, 0.7, 2]
    else:  # 5 components
        x_positions = [-2.5, -1.25, 0, 1.25, 2.5]
    
    for j, component in enumerate(layer['components']):
        x_pos = x_positions[j]
        all_x.append(x_pos)
        all_y.append(layer_y)
        
        # Component names already shortened in data
        all_text.append(component['name'])
        all_colors.append(colors.get(component['type'], '#1FB8CD'))
        
        # Create hover text with more details
        hover_text = f"<b>{component['name']}</b><br>Layer: {layer['name']}<br>Type: {component['type']}"
        all_hover.append(hover_text)

# Add scatter plot for components with larger, more readable text
fig.add_trace(go.Scatter(
    x=all_x,
    y=all_y,
    text=all_text,
    mode='markers+text',
    marker=dict(
        size=120,
        color=all_colors,
        line=dict(width=3, color='white'),
        opacity=0.9
    ),
    textposition='middle center',
    textfont=dict(size=11, color='white', family='Arial Black'),
    hovertext=all_hover,
    hoverinfo='text',
    showlegend=False
))

# Add directional arrows between layers to show data flow
arrow_props = dict(
    type="line",
    line=dict(color="rgba(80,80,80,0.6)", width=3),
    layer="below"
)

# Frontend to Security layer arrows
for x_front in [-1, 1]:  # Frontend positions
    for x_sec in [-2, -0.7, 0.7, 2]:  # Security positions
        fig.add_shape(
            x0=x_front, y0=3.7,
            x1=x_sec, y1=3.3,
            **arrow_props
        )

# Security to Backend arrows
for x_sec in [-2, -0.7, 0.7, 2]:  # Security positions  
    for x_back in [-2.5, -1.25, 0, 1.25, 2.5]:  # Backend positions
        if abs(x_sec - x_back) < 1.5:  # Only connect nearby components
            fig.add_shape(
                x0=x_sec, y0=2.7,
                x1=x_back, y1=2.3,
                **arrow_props
            )

# Backend to Database arrows
for x_back in [-2.5, -1.25, 0, 1.25, 2.5]:  # Backend positions
    fig.add_shape(
        x0=x_back, y0=1.7,
        x1=0, y1=1.3,  # Database at center
        **arrow_props
    )

# Backend to External Services arrows
for i, x_back in enumerate([-2.5, -1.25, 2.5]):  # Select backend services
    for j, x_ext in enumerate([-1.5, 0, 1.5]):  # External services
        if (i == 0 and j == 1) or (i == 1 and j == 0) or (i == 2 and j == 2):  # Specific connections
            fig.add_shape(
                x0=x_back, y0=1.7,
                x1=x_ext, y1=0.3,
                **arrow_props
            )

# Update axes
fig.update_xaxes(
    range=[-3.5, 3.5],
    showgrid=False,
    showticklabels=False,
    zeroline=False
)

fig.update_yaxes(
    range=[-0.7, 4.7],
    showgrid=False,
    zeroline=False,
    tickvals=y_positions,
    ticktext=[layer['name'] for layer in data['layers']],
    tickfont=dict(size=13, color='#333333'),
    side='left'
)

# Update layout with better styling
fig.update_layout(
    title="SaaS Translation Platform Architecture",
    showlegend=False,
    plot_bgcolor='rgba(0,0,0,0)',
    paper_bgcolor='white'
)

# Save the chart
fig.write_image("saas_architecture.png", width=1200, height=800)