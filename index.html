<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能翻译平台 - SaaS实时翻译服务</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 首页登录选择 -->
    <div id="homepage" class="page active">
        <div class="hero-section">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">智能翻译平台</h1>
                    <p class="hero-subtitle">专业的SaaS实时翻译服务，支持语音识别和多语言翻译</p>
                    <div class="login-options">
                        <button class="btn btn--primary btn--lg" id="user-login-btn">
                            <i class="fas fa-user"></i>
                            用户登录
                        </button>
                        <button class="btn btn--secondary btn--lg" id="admin-login-btn">
                            <i class="fas fa-user-shield"></i>
                            管理员登录
                        </button>
                    </div>
                    <div class="platform-features">
                        <div class="feature-card">
                            <i class="fas fa-microphone"></i>
                            <h3>语音识别</h3>
                            <p>支持实时语音输入翻译</p>
                        </div>
                        <div class="feature-card">
                            <i class="fas fa-globe"></i>
                            <h3>多语言支持</h3>
                            <p>支持50+种语言互译</p>
                        </div>
                        <div class="feature-card">
                            <i class="fas fa-chart-line"></i>
                            <h3>专业分析</h3>
                            <p>详细的使用统计和分析</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户登录页面 -->
    <div id="user-login" class="page">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h2>用户登录</h2>
                    <p>登录您的翻译账户</p>
                </div>
                <form class="auth-form" id="user-login-form">
                    <div class="form-group">
                        <label class="form-label">邮箱地址</label>
                        <input type="email" class="form-control" value="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" value="password" required>
                    </div>
                    <button type="submit" class="btn btn--primary btn--full-width">登录</button>
                </form>
                <div class="auth-footer">
                    <button class="btn btn--outline" id="back-to-home-from-user">返回首页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员登录页面 -->
    <div id="admin-login" class="page">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h2>管理员登录</h2>
                    <p>管理员控制面板入口</p>
                </div>
                <form class="auth-form" id="admin-login-form">
                    <div class="form-group">
                        <label class="form-label">管理员邮箱</label>
                        <input type="email" class="form-control" value="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" value="admin123" required>
                    </div>
                    <button type="submit" class="btn btn--primary btn--full-width">登录</button>
                </form>
                <div class="auth-footer">
                    <button class="btn btn--outline" id="back-to-home-from-admin">返回首页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户端主界面 -->
    <div id="user-dashboard" class="page">
        <div class="dashboard-layout">
            <!-- 顶部导航 -->
            <header class="dashboard-header">
                <div class="header-left">
                    <h1 class="dashboard-title">智能翻译平台</h1>
                    <div class="breadcrumb">
                        <span>用户中心</span>
                        <span id="current-page-name">翻译</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <span>欢迎，用户</span>
                        <button class="btn btn--sm btn--outline" id="user-logout-btn">退出</button>
                    </div>
                </div>
            </header>

            <!-- 侧边栏 -->
            <aside class="dashboard-sidebar">
                <nav class="sidebar-nav">
                    <a href="#" class="nav-item active" data-section="translate">
                        <i class="fas fa-language"></i>
                        <span>实时翻译</span>
                    </a>
                    <a href="#" class="nav-item" data-section="dashboard">
                        <i class="fas fa-chart-pie"></i>
                        <span>个人仪表板</span>
                    </a>
                    <a href="#" class="nav-item" data-section="subscription">
                        <i class="fas fa-credit-card"></i>
                        <span>订阅管理</span>
                    </a>
                    <a href="#" class="nav-item" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </a>
                </nav>
            </aside>

            <!-- 主内容区 -->
            <main class="dashboard-main">
                <!-- 实时翻译界面 -->
                <div id="translate-section" class="dashboard-section active">
                    <div class="translate-container">
                        <div class="translate-header">
                            <h2>实时翻译</h2>
                            <div class="language-selector">
                                <select id="source-lang" class="form-control">
                                    <option value="auto">自动检测</option>
                                    <option value="zh">中文</option>
                                    <option value="en">英语</option>
                                    <option value="ja">日语</option>
                                    <option value="ko">韩语</option>
                                    <option value="fr">法语</option>
                                </select>
                                <button class="swap-btn" id="swap-languages-btn">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                                <select id="target-lang" class="form-control">
                                    <option value="zh">中文</option>
                                    <option value="en" selected>英语</option>
                                    <option value="ja">日语</option>
                                    <option value="ko">韩语</option>
                                    <option value="fr">法语</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="translate-panels">
                            <div class="translate-panel">
                                <div class="panel-header">
                                    <span>输入文本</span>
                                    <button class="voice-btn" id="voice-input-btn">
                                        <i class="fas fa-microphone"></i>
                                    </button>
                                </div>
                                <textarea id="source-text" class="translate-textarea" placeholder="请输入要翻译的文本或点击麦克风按钮进行语音输入..."></textarea>
                            </div>
                            
                            <div class="translate-panel">
                                <div class="panel-header">
                                    <span>翻译结果</span>
                                    <button class="copy-btn" id="copy-translation-btn">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <textarea id="target-text" class="translate-textarea" readonly placeholder="翻译结果将显示在这里..."></textarea>
                            </div>
                        </div>
                        
                        <div class="translate-actions">
                            <button class="btn btn--primary" id="translate-btn">翻译</button>
                            <button class="btn btn--secondary" id="clear-text-btn">清空</button>
                            <button class="btn btn--outline" id="save-translation-btn">保存</button>
                        </div>
                    </div>
                </div>

                <!-- 个人仪表板 -->
                <div id="dashboard-section" class="dashboard-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-language"></i>
                            </div>
                            <div class="stat-content">
                                <h3>本月翻译</h3>
                                <div class="stat-number">1,234</div>
                                <div class="stat-change">+15%</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3>使用时长</h3>
                                <div class="stat-number">45小时</div>
                                <div class="stat-change">+8%</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h3>准确率</h3>
                                <div class="stat-number">98.5%</div>
                                <div class="stat-change">+2%</div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-charts">
                        <div class="chart-container">
                            <h3>使用趋势</h3>
                            <div style="position: relative; height: 300px;">
                                <canvas id="usage-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="recent-translations">
                        <h3>最近翻译</h3>
                        <div class="translation-list" id="translation-history">
                            <!-- 翻译历史将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 订阅管理 -->
                <div id="subscription-section" class="dashboard-section">
                    <div class="subscription-header">
                        <h2>订阅管理</h2>
                        <div class="current-plan">
                            <span class="plan-badge">当前计划：专业版</span>
                        </div>
                    </div>
                    
                    <div class="plan-cards">
                        <div class="plan-card">
                            <div class="plan-header">
                                <h3>免费版</h3>
                                <div class="plan-price">¥0<span>/月</span></div>
                            </div>
                            <ul class="plan-features">
                                <li>100次翻译/月</li>
                                <li>基础语言对</li>
                                <li>标准速度</li>
                            </ul>
                            <button class="btn btn--outline">当前计划</button>
                        </div>
                        
                        <div class="plan-card recommended">
                            <div class="plan-header">
                                <h3>基础版</h3>
                                <div class="plan-price">¥9.99<span>/月</span></div>
                            </div>
                            <ul class="plan-features">
                                <li>5000次翻译/月</li>
                                <li>所有语言对</li>
                                <li>优先处理</li>
                            </ul>
                            <button class="btn btn--primary">升级</button>
                        </div>
                        
                        <div class="plan-card">
                            <div class="plan-header">
                                <h3>专业版</h3>
                                <div class="plan-price">¥29.99<span>/月</span></div>
                            </div>
                            <ul class="plan-features">
                                <li>无限翻译</li>
                                <li>API访问</li>
                                <li>高级分析</li>
                                <li>优先支持</li>
                            </ul>
                            <button class="btn btn--primary">升级</button>
                        </div>
                    </div>
                </div>

                <!-- 设置页面 -->
                <div id="settings-section" class="dashboard-section">
                    <div class="settings-container">
                        <h2>设置</h2>
                        
                        <div class="settings-group">
                            <h3>账户设置</h3>
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-control" value="用户">
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮箱</label>
                                <input type="email" class="form-control" value="<EMAIL>">
                            </div>
                        </div>
                        
                        <div class="settings-group">
                            <h3>翻译设置</h3>
                            <div class="form-group">
                                <label class="form-label">默认源语言</label>
                                <select class="form-control">
                                    <option value="auto">自动检测</option>
                                    <option value="zh">中文</option>
                                    <option value="en">英语</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">默认目标语言</label>
                                <select class="form-control">
                                    <option value="en">英语</option>
                                    <option value="zh">中文</option>
                                    <option value="ja">日语</option>
                                </select>
                            </div>
                        </div>
                        
                        <button class="btn btn--primary">保存设置</button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 管理员界面 -->
    <div id="admin-dashboard" class="page">
        <div class="dashboard-layout">
            <!-- 管理员顶部导航 -->
            <header class="dashboard-header">
                <div class="header-left">
                    <h1 class="dashboard-title">管理员控制面板</h1>
                    <div class="breadcrumb">
                        <span>管理中心</span>
                        <span id="admin-current-page">数据分析</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <span>管理员</span>
                        <button class="btn btn--sm btn--outline" id="admin-logout-btn">退出</button>
                    </div>
                </div>
            </header>

            <!-- 管理员侧边栏 -->
            <aside class="dashboard-sidebar">
                <nav class="sidebar-nav">
                    <a href="#" class="nav-item active" data-admin-section="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </a>
                    <a href="#" class="nav-item" data-admin-section="users">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="#" class="nav-item" data-admin-section="discounts">
                        <i class="fas fa-tags"></i>
                        <span>折扣券管理</span>
                    </a>
                    <a href="#" class="nav-item" data-admin-section="system">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                </nav>
            </aside>

            <!-- 管理员主内容区 -->
            <main class="dashboard-main">
                <!-- 数据分析 -->
                <div id="analytics-section" class="dashboard-section active">
                    <div class="admin-stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3>总用户数</h3>
                                <div class="stat-number">15,642</div>
                                <div class="stat-change">+12%</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-content">
                                <h3>活跃用户</h3>
                                <div class="stat-number">8,234</div>
                                <div class="stat-change">+8%</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h3>月收入</h3>
                                <div class="stat-number">¥45,678</div>
                                <div class="stat-change">+25%</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-language"></i>
                            </div>
                            <div class="stat-content">
                                <h3>今日翻译</h3>
                                <div class="stat-number">2,341</div>
                                <div class="stat-change">+5%</div>
                            </div>
                        </div>
                    </div>

                    <div class="admin-charts">
                        <div class="chart-container">
                            <h3>用户增长趋势</h3>
                            <div style="position: relative; height: 300px;">
                                <canvas id="user-growth-chart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container">
                            <h3>收入增长趋势</h3>
                            <div style="position: relative; height: 300px;">
                                <canvas id="revenue-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理 -->
                <div id="users-section" class="dashboard-section">
                    <div class="section-header">
                        <h2>用户管理</h2>
                        <div class="section-actions">
                            <input type="text" class="form-control" placeholder="搜索用户..." id="user-search">
                            <button class="btn btn--primary" id="search-users-btn">搜索</button>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>姓名</th>
                                    <th>邮箱</th>
                                    <th>计划</th>
                                    <th>使用量</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body">
                                <!-- 用户数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 折扣券管理 -->
                <div id="discounts-section" class="dashboard-section">
                    <div class="section-header">
                        <h2>折扣券管理</h2>
                        <button class="btn btn--primary" id="create-discount-btn">创建折扣券</button>
                    </div>
                    
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>折扣码</th>
                                    <th>折扣</th>
                                    <th>已使用</th>
                                    <th>限制</th>
                                    <th>到期日期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="discounts-table-body">
                                <!-- 折扣券数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div id="system-section" class="dashboard-section">
                    <div class="settings-container">
                        <h2>系统设置</h2>
                        
                        <div class="settings-group">
                            <h3>API配置</h3>
                            <div class="form-group">
                                <label class="form-label">翻译API密钥</label>
                                <input type="password" class="form-control" value="sk-xxxxxxxxxxxxxxxx">
                            </div>
                            <div class="form-group">
                                <label class="form-label">API请求限制（次/分钟）</label>
                                <input type="number" class="form-control" value="100">
                            </div>
                        </div>
                        
                        <div class="settings-group">
                            <h3>系统参数</h3>
                            <div class="form-group">
                                <label class="form-label">免费用户月度限制</label>
                                <input type="number" class="form-control" value="100">
                            </div>
                            <div class="form-group">
                                <label class="form-label">系统维护模式</label>
                                <select class="form-control">
                                    <option value="off">关闭</option>
                                    <option value="on">开启</option>
                                </select>
                            </div>
                        </div>
                        
                        <button class="btn btn--primary">保存设置</button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建折扣券模态框 -->
    <div id="create-discount-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>创建折扣券</h3>
                <button class="modal-close" id="close-modal-btn">×</button>
            </div>
            <div class="modal-body">
                <form id="create-discount-form">
                    <div class="form-group">
                        <label class="form-label">折扣码</label>
                        <input type="text" class="form-control" id="discount-code" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">折扣类型</label>
                        <select class="form-control" id="discount-type">
                            <option value="percentage">百分比</option>
                            <option value="fixed">固定金额</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">折扣值</label>
                        <input type="number" class="form-control" id="discount-value" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">使用限制</label>
                        <input type="number" class="form-control" id="discount-limit" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">到期日期</label>
                        <input type="date" class="form-control" id="discount-expires" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn--outline" id="cancel-discount-btn">取消</button>
                <button class="btn btn--primary" id="submit-discount-btn">创建</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>