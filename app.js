// 应用数据
const appData = {
  users: [
    {"id": 1, "name": "张三", "email": "<EMAIL>", "plan": "专业版", "usage": 1200, "status": "活跃"},
    {"id": 2, "name": "李四", "email": "<EMAIL>", "plan": "基础版", "usage": 300, "status": "活跃"},
    {"id": 3, "name": "王五", "email": "<EMAIL>", "plan": "免费版", "usage": 50, "status": "暂停"}
  ],
  discounts: [
    {"id": 1, "code": "WELCOME20", "discount": "20%", "used": 45, "limit": 100, "expires": "2025-12-31"},
    {"id": 2, "code": "STUDENT50", "discount": "50%", "used": 23, "limit": 50, "expires": "2025-06-30"}
  ],
  translations: [
    {"date": "2025-08-01", "source": "Hello world", "target": "你好世界", "language": "en-zh"},
    {"date": "2025-08-01", "source": "Good morning", "target": "早上好", "language": "en-zh"},
    {"date": "2025-07-31", "source": "Thank you", "target": "谢谢", "language": "en-zh"}
  ],
  analytics: {
    "totalUsers": 15642,
    "activeUsers": 8234,
    "monthlyRevenue": 45678,
    "translationsToday": 2341,
    "userGrowth": [1200, 1350, 1500, 1800, 2100, 2400],
    "revenueGrowth": [12000, 15000, 18000, 22000, 28000, 35000]
  },
  plans: [
    {"name": "免费版", "price": 0, "features": ["100次翻译/月", "基础语言对", "标准速度"]},
    {"name": "基础版", "price": 9.99, "features": ["5000次翻译/月", "所有语言对", "优先处理"]},
    {"name": "专业版", "price": 29.99, "features": ["无限翻译", "API访问", "高级分析", "优先支持"]}
  ]
};

// 全局变量
let currentUser = null;
let currentUserType = null;
let isVoiceRecording = false;
let recognition = null;
let usageChart = null;
let userGrowthChart = null;
let revenueChart = null;

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    // 初始化语音识别
    initSpeechRecognition();
    
    // 显示首页
    showPage('homepage');
});

// 页面导航
function showPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.classList.remove('active'));
    
    // 显示目标页面
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.add('active');
    }
}

// 用户登录
function userLogin(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const email = event.target.querySelector('input[type="email"]').value.trim();
    const password = event.target.querySelector('input[type="password"]').value.trim();
    
    console.log('用户登录尝试:', email, password); // 调试日志
    
    // 模拟登录验证 - 使用简单的验证逻辑
    if (email && password && (email === '<EMAIL>' || email.includes('@')) && password.length > 0) {
        currentUser = { name: '用户', email: email, plan: '专业版' };
        currentUserType = 'user';
        
        showPage('user-dashboard');
        showUserSection('translate');
        
        // 延迟加载图表，确保DOM已渲染
        setTimeout(() => {
            initUserCharts();
            loadTranslationHistory();
        }, 100);
        
        showNotification('登录成功！', 'success');
    } else {
        showNotification('请输入有效的邮箱和密码', 'error');
    }
}

// 管理员登录
function adminLogin(event) {
    event.preventDefault();
    
    const email = event.target.querySelector('input[type="email"]').value.trim();
    const password = event.target.querySelector('input[type="password"]').value.trim();
    
    console.log('管理员登录尝试:', email, password); // 调试日志
    
    // 模拟管理员登录验证 - 使用简单的验证逻辑
    if (email && password && (email === '<EMAIL>' || email.includes('admin')) && password.length > 0) {
        currentUser = { name: '管理员', email: email };
        currentUserType = 'admin';
        
        showPage('admin-dashboard');
        showAdminSection('analytics');
        
        // 延迟加载图表和数据
        setTimeout(() => {
            initAdminCharts();
            loadUsersTable();
            loadDiscountsTable();
        }, 100);
        
        showNotification('管理员登录成功！', 'success');
    } else {
        showNotification('请输入有效的管理员邮箱和密码', 'error');
    }
}

// 退出登录
function logout() {
    currentUser = null;
    currentUserType = null;
    
    // 销毁图表实例
    if (usageChart) {
        usageChart.destroy();
        usageChart = null;
    }
    if (userGrowthChart) {
        userGrowthChart.destroy();
        userGrowthChart = null;
    }
    if (revenueChart) {
        revenueChart.destroy();
        revenueChart = null;
    }
    
    showPage('homepage');
    showNotification('已退出登录', 'info');
}

// 显示用户区域
function showUserSection(sectionId) {
    // 更新导航状态
    const navItems = document.querySelectorAll('#user-dashboard .nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    
    const activeNavItem = document.querySelector(`#user-dashboard .nav-item[onclick="showUserSection('${sectionId}')"]`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
    
    // 显示对应区域
    const sections = document.querySelectorAll('#user-dashboard .dashboard-section');
    sections.forEach(section => section.classList.remove('active'));
    
    const targetSection = document.getElementById(`${sectionId}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // 更新面包屑
    const pageNames = {
        'translate': '实时翻译',
        'dashboard': '个人仪表板',
        'subscription': '订阅管理',
        'settings': '设置'
    };
    
    const currentPageName = document.getElementById('current-page-name');
    if (currentPageName && pageNames[sectionId]) {
        currentPageName.textContent = pageNames[sectionId];
    }
}

// 显示管理员区域
function showAdminSection(sectionId) {
    // 更新导航状态
    const navItems = document.querySelectorAll('#admin-dashboard .nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    
    const activeNavItem = document.querySelector(`#admin-dashboard .nav-item[onclick="showAdminSection('${sectionId}')"]`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
    
    // 显示对应区域
    const sections = document.querySelectorAll('#admin-dashboard .dashboard-section');
    sections.forEach(section => section.classList.remove('active'));
    
    const targetSection = document.getElementById(`${sectionId}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // 更新面包屑
    const pageNames = {
        'analytics': '数据分析',
        'users': '用户管理',
        'discounts': '折扣券管理',
        'system': '系统设置'
    };
    
    const adminCurrentPage = document.getElementById('admin-current-page');
    if (adminCurrentPage && pageNames[sectionId]) {
        adminCurrentPage.textContent = pageNames[sectionId];
    }
}

// 初始化语音识别
function initSpeechRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'zh-CN';
        
        recognition.onstart = function() {
            isVoiceRecording = true;
            const voiceBtn = document.querySelector('.voice-btn');
            if (voiceBtn) {
                voiceBtn.classList.add('voice-recording');
                voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
            }
        };
        
        recognition.onresult = function(event) {
            const result = event.results[0][0].transcript;
            const sourceText = document.getElementById('source-text');
            if (sourceText) {
                sourceText.value = result;
                // 自动触发翻译
                translateText();
            }
        };
        
        recognition.onend = function() {
            isVoiceRecording = false;
            const voiceBtn = document.querySelector('.voice-btn');
            if (voiceBtn) {
                voiceBtn.classList.remove('voice-recording');
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            }
        };
        
        recognition.onerror = function(event) {
            console.error('语音识别错误:', event.error);
            showNotification('语音识别失败，请重试', 'error');
        };
    }
}

// 开始语音输入
function startVoiceInput() {
    if (!recognition) {
        showNotification('您的浏览器不支持语音识别功能', 'warning');
        return;
    }
    
    if (isVoiceRecording) {
        recognition.stop();
    } else {
        try {
            recognition.start();
            showNotification('开始语音识别...', 'info');
        } catch (error) {
            console.error('语音识别启动失败:', error);
            showNotification('语音识别启动失败', 'error');
        }
    }
}

// 翻译文本
function translateText() {
    const sourceText = document.getElementById('source-text');
    const targetText = document.getElementById('target-text');
    const sourceLang = document.getElementById('source-lang');
    const targetLang = document.getElementById('target-lang');
    
    if (!sourceText || !targetText) return;
    
    const text = sourceText.value.trim();
    if (!text) {
        showNotification('请输入要翻译的文本', 'warning');
        return;
    }
    
    // 显示加载状态
    targetText.value = '正在翻译...';
    
    // 模拟翻译API调用
    setTimeout(() => {
        const translations = {
            'Hello world': '你好世界',
            'hello world': '你好世界',
            'Hello World': '你好世界',
            'Good morning': '早上好',
            'good morning': '早上好',
            'Thank you': '谢谢',
            'thank you': '谢谢',
            'How are you?': '你好吗？',
            'how are you': '你好吗？',
            'Nice to meet you': '很高兴见到你',
            'nice to meet you': '很高兴见到你',
            '你好': 'Hello',
            '早上好': 'Good morning',
            '谢谢': 'Thank you',
            '再见': 'Goodbye',
            '很高兴见到你': 'Nice to meet you'
        };
        
        let translatedText = translations[text] || translations[text.toLowerCase()];
        if (!translatedText) {
            // 简单的模拟翻译
            const sourceLanguage = sourceLang ? sourceLang.value : 'auto';
            const targetLanguage = targetLang ? targetLang.value : 'zh';
            
            if (sourceLanguage === 'en' || sourceLanguage === 'auto') {
                translatedText = `翻译结果: ${text}`;
            } else {
                translatedText = `Translation: ${text}`;
            }
        }
        
        targetText.value = translatedText;
        
        // 保存到翻译历史
        const newTranslation = {
            date: new Date().toISOString().split('T')[0],
            source: text,
            target: translatedText,
            language: `${sourceLang ? sourceLang.value : 'auto'}-${targetLang ? targetLang.value : 'zh'}`
        };
        
        appData.translations.unshift(newTranslation);
        if (appData.translations.length > 10) {
            appData.translations.pop();
        }
        
        // 刷新翻译历史显示
        loadTranslationHistory();
        
        showNotification('翻译完成', 'success');
    }, 1000);
}

// 交换语言
function swapLanguages() {
    const sourceLang = document.getElementById('source-lang');
    const targetLang = document.getElementById('target-lang');
    const sourceText = document.getElementById('source-text');
    const targetText = document.getElementById('target-text');
    
    if (sourceLang && targetLang) {
        const tempLang = sourceLang.value;
        sourceLang.value = targetLang.value;
        targetLang.value = tempLang;
    }
    
    if (sourceText && targetText) {
        const tempText = sourceText.value;
        sourceText.value = targetText.value;
        targetText.value = tempText;
    }
    
    showNotification('语言已交换', 'info');
}

// 复制翻译结果
function copyTranslation() {
    const targetText = document.getElementById('target-text');
    if (targetText && targetText.value.trim()) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(targetText.value).then(() => {
                showNotification('已复制到剪贴板', 'success');
            }).catch(() => {
                // 备用复制方法
                fallbackCopyText(targetText.value);
            });
        } else {
            // 备用复制方法
            fallbackCopyText(targetText.value);
        }
    } else {
        showNotification('没有可复制的内容', 'warning');
    }
}

// 备用复制方法
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
        showNotification('已复制到剪贴板', 'success');
    } catch (err) {
        showNotification('复制失败', 'error');
    }
    document.body.removeChild(textArea);
}

// 清空文本
function clearText() {
    const sourceText = document.getElementById('source-text');
    const targetText = document.getElementById('target-text');
    
    if (sourceText) sourceText.value = '';
    if (targetText) targetText.value = '';
    
    showNotification('文本已清空', 'info');
}

// 保存翻译
function saveTranslation() {
    const sourceText = document.getElementById('source-text');
    const targetText = document.getElementById('target-text');
    
    if (sourceText && targetText && sourceText.value.trim() && targetText.value.trim()) {
        showNotification('翻译已保存到历史记录', 'success');
    } else {
        showNotification('没有可保存的翻译内容', 'warning');
    }
}

// 初始化用户图表
function initUserCharts() {
    const usageChartCanvas = document.getElementById('usage-chart');
    if (usageChartCanvas && !usageChart) {
        const ctx = usageChartCanvas.getContext('2d');
        usageChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天'],
                datasets: [{
                    label: '翻译次数',
                    data: [45, 67, 89, 123, 156, 189, 234],
                    borderColor: '#1FB8CD',
                    backgroundColor: 'rgba(31, 184, 205, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
}

// 初始化管理员图表
function initAdminCharts() {
    // 用户增长图表
    const userGrowthCanvas = document.getElementById('user-growth-chart');
    if (userGrowthCanvas && !userGrowthChart) {
        const ctx = userGrowthCanvas.getContext('2d');
        userGrowthChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '用户数量',
                    data: appData.analytics.userGrowth,
                    borderColor: '#1FB8CD',
                    backgroundColor: 'rgba(31, 184, 205, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
    
    // 收入增长图表
    const revenueCanvas = document.getElementById('revenue-chart');
    if (revenueCanvas && !revenueChart) {
        const ctx = revenueCanvas.getContext('2d');
        revenueChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '收入 (元)',
                    data: appData.analytics.revenueGrowth,
                    backgroundColor: '#FFC185',
                    borderColor: '#FFC185',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
}

// 加载翻译历史
function loadTranslationHistory() {
    const historyContainer = document.getElementById('translation-history');
    if (!historyContainer) return;
    
    historyContainer.innerHTML = '';
    
    appData.translations.forEach(translation => {
        const item = document.createElement('div');
        item.className = 'translation-item';
        item.innerHTML = `
            <div class="translation-meta">
                <span>${translation.language}</span>
                <span>${translation.date}</span>
            </div>
            <div class="translation-content">
                <div class="translation-text">${translation.source}</div>
                <div class="translation-text">${translation.target}</div>
            </div>
        `;
        historyContainer.appendChild(item);
    });
}

// 加载用户表格
function loadUsersTable() {
    const tbody = document.getElementById('users-table-body');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    appData.users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.name}</td>
            <td>${user.email}</td>
            <td>${user.plan}</td>
            <td>${user.usage}</td>
            <td><span class="status-badge ${user.status === '活跃' ? 'active' : 'inactive'}">${user.status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit" onclick="editUser(${user.id})">编辑</button>
                    <button class="action-btn delete" onclick="deleteUser(${user.id})">删除</button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 加载折扣券表格
function loadDiscountsTable() {
    const tbody = document.getElementById('discounts-table-body');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    appData.discounts.forEach(discount => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${discount.id}</td>
            <td>${discount.code}</td>
            <td>${discount.discount}</td>
            <td>${discount.used}</td>
            <td>${discount.limit}</td>
            <td>${discount.expires}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit" onclick="editDiscount(${discount.id})">编辑</button>
                    <button class="action-btn delete" onclick="deleteDiscount(${discount.id})">删除</button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 搜索用户
function searchUsers() {
    const searchInput = document.getElementById('user-search');
    const searchTerm = searchInput.value.toLowerCase();
    
    const filteredUsers = appData.users.filter(user => 
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm) ||
        user.plan.toLowerCase().includes(searchTerm)
    );
    
    const tbody = document.getElementById('users-table-body');
    tbody.innerHTML = '';
    
    filteredUsers.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.name}</td>
            <td>${user.email}</td>
            <td>${user.plan}</td>
            <td>${user.usage}</td>
            <td><span class="status-badge ${user.status === '活跃' ? 'active' : 'inactive'}">${user.status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit" onclick="editUser(${user.id})">编辑</button>
                    <button class="action-btn delete" onclick="deleteUser(${user.id})">删除</button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    showNotification(`找到 ${filteredUsers.length} 个用户`, 'info');
}

// 编辑用户
function editUser(userId) {
    const user = appData.users.find(u => u.id === userId);
    if (user) {
        showNotification(`编辑用户: ${user.name}`, 'info');
        // 这里可以打开编辑模态框
    }
}

// 删除用户
function deleteUser(userId) {
    if (confirm('确定要删除此用户吗？')) {
        const index = appData.users.findIndex(u => u.id === userId);
        if (index > -1) {
            appData.users.splice(index, 1);
            loadUsersTable();
            showNotification('用户已删除', 'success');
        }
    }
}

// 编辑折扣券
function editDiscount(discountId) {
    const discount = appData.discounts.find(d => d.id === discountId);
    if (discount) {
        showNotification(`编辑折扣券: ${discount.code}`, 'info');
        // 这里可以打开编辑模态框
    }
}

// 删除折扣券
function deleteDiscount(discountId) {
    if (confirm('确定要删除此折扣券吗？')) {
        const index = appData.discounts.findIndex(d => d.id === discountId);
        if (index > -1) {
            appData.discounts.splice(index, 1);
            loadDiscountsTable();
            showNotification('折扣券已删除', 'success');
        }
    }
}

// 显示创建折扣券模态框
function showCreateDiscountModal() {
    const modal = document.getElementById('create-discount-modal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

// 隐藏创建折扣券模态框
function hideCreateDiscountModal() {
    const modal = document.getElementById('create-discount-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
    
    // 清空表单
    const form = document.getElementById('create-discount-form');
    if (form) {
        form.reset();
    }
}

// 创建折扣券
function createDiscount() {
    const code = document.getElementById('discount-code').value.trim();
    const type = document.getElementById('discount-type').value;
    const value = document.getElementById('discount-value').value.trim();
    const limit = document.getElementById('discount-limit').value.trim();
    const expires = document.getElementById('discount-expires').value;
    
    if (!code || !value || !limit || !expires) {
        showNotification('请填写所有必需字段', 'error');
        return;
    }
    
    const newDiscount = {
        id: appData.discounts.length + 1,
        code: code,
        discount: type === 'percentage' ? `${value}%` : `¥${value}`,
        used: 0,
        limit: parseInt(limit),
        expires: expires
    };
    
    appData.discounts.push(newDiscount);
    loadDiscountsTable();
    hideCreateDiscountModal();
    showNotification('折扣券创建成功', 'success');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 2000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    // 设置颜色
    const colors = {
        success: '#22c55e',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    
    notification.style.backgroundColor = colors[type] || colors.info;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 模拟API调用的工具函数
function simulateApiCall(callback, delay = 1000) {
    return new Promise((resolve) => {
        setTimeout(() => {
            const result = callback();
            resolve(result);
        }, delay);
    });
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 获取随机颜色
function getRandomColor() {
    const colors = ['#1FB8CD', '#FFC185', '#B4413C', '#ECEBD5', '#5D878F', '#DB4545', '#D2BA4C', '#964325', '#944454', '#13343B'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 检测移动设备
function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 页面可见性检测
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停某些操作
        console.log('页面已隐藏');
    } else {
        // 页面可见时恢复操作
        console.log('页面已显示');
    }
});

// 错误处理
window.addEventListener('error', function(event) {
    console.error('JavaScript错误:', event.error);
    showNotification('发生了一个错误，请刷新页面重试', 'error');
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise拒绝:', event.reason);
    event.preventDefault();
});

// 导出功能（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        appData,
        showPage,
        userLogin,
        adminLogin,
        logout,
        translateText,
        showNotification
    };
}